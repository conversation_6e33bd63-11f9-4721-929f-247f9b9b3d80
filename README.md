# LauncherDex

This project builds the `launcher.dex` file used by [LeviLaunchroid](https://github.com/LiteLDev/<PERSON>Launchroid), a third-party Minecraft Pocket Edition launcher. The generated DEX file contains essential Java classes that enable Minecraft launching functionality within the LeviLaunchroid Android application.

## About

LeviLaunchroid is a third-party launcher for Minecraft Pocket Edition that requires specific Java classes to function properly. This project compiles those classes into a DEX file (`launcher.dex`) that gets placed in the LeviLaunchroid app's assets directory (`app/src/main/assets/launcher.dex`).

## Project Structure

```
LauncherDex/
├── src/main/java/com/mojang/minecraftpe/
│   ├── Launcher.java                    # Main launcher class (kept)
│   ├── NotificationListenerService.java # Notification service (kept)
│   └── store/
│       ├── amazonappstore/
│       │   └── AmazonAppStore.java      # Amazon store integration (kept)
│       └── googleplay/
│           └── GooglePlayStore.java     # Google Play integration (kept)
├── build.gradle                        # Gradle build configuration
├── build.bat                          # Windows build script
├── build.sh                           # Linux/macOS build script
├── modify_dex.py                      # Python script to remove unused classes
├── gradle.properties
├── settings.gradle
└── README.md
```

**Note**: Several classes are removed during the build process to make the launcher work properly. The removed classes include MainActivity, FilePickerManagerHandler, and various store-related classes that are not needed for the launcher functionality.

## Quick Start

If you want to modify the classes or build the DEX file yourself:

## Prerequisites

1. **Java Development Kit (JDK) 8 or higher**
   - Download from: https://adoptium.net/
   - Verify installation: `java -version`

2. **Android SDK**
   - Install Android Studio or standalone SDK tools
   - Set the `ANDROID_HOME` environment variable to your Android SDK path
   - **Windows**: `ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk`
   - **Linux**: `ANDROID_HOME=/home/<USER>/Android/Sdk`
   - **macOS**: `ANDROID_HOME=/Users/<USER>/Library/Android/sdk`

3. **Python 3.6+** (for the DEX modification script)
   - Download from: https://python.org/

## Building the DEX File

### Method 1: Automated Build (Recommended)

This method builds the complete optimized `launcher.dex` file ready for use in LeviLaunchroid:

```bash
# Clone the repository
git clone https://github.com/LiteLDev/LauncherDex.git
cd LauncherDex

# Set your Android SDK path
export ANDROID_HOME=/path/to/your/android/sdk  # Linux/macOS
# OR
set ANDROID_HOME=C:\path\to\your\android\sdk   # Windows

# Build the optimized launcher.dex
./build.sh          # Linux/macOS
# OR
build.bat           # Windows

# The final launcher.dex will be in build/libs/launcher.dex
```

### Method 2: Using Gradle

```bash
# Build using Gradle wrapper
./gradlew createDex     # Linux/macOS
gradlew.bat createDex   # Windows

# Then optimize the DEX file
python modify_dex.py
```

### Output

After building, you'll find:
- **`build/libs/launcher.dex`** - The DEX file for LeviLaunchroid (≈5.8KB)
- **`build/libs/LauncherDex-1.0.jar`** - Intermediate JAR file (≈8.7KB)

## Integration with LeviLaunchroid

### Step 1: Build the DEX file
Follow the build instructions above to generate `launcher.dex`.

### Step 2: Copy to LeviLaunchroid
Copy the generated `launcher.dex` file to your LeviLaunchroid project:

```bash
# Copy launcher.dex to LeviLaunchroid assets
cp build/libs/launcher.dex /path/to/LeviLaunchroid/app/src/main/assets/launcher.dex
```

### Step 3: Build LeviLaunchroid
Build your LeviLaunchroid app as usual. The launcher.dex will be included in the APK and loaded at runtime to provide Minecraft launching capabilities.

## What's in the DEX file?

The optimized `launcher.dex` contains only the essential classes needed for Minecraft launching:

### Included Classes:
- **`com.mojang.minecraftpe.Launcher`** - Main launcher functionality
- **`com.mojang.minecraftpe.NotificationListenerService`** - Notification handling
- **`com.mojang.minecraftpe.store.amazonappstore.AmazonAppStore`** - Amazon App Store integration
- **`com.mojang.minecraftpe.store.googleplay.GooglePlayStore`** - Google Play Store integration

### Removed Classes (to make launcher work):
- `MainActivity` - Not needed for launcher functionality
- `FilePickerManagerHandler` - File picker interface
- `ExtraLicenseResponseData` - License response data
- `Product`, `Purchase`, `Store`, `StoreListener` - Store-related interfaces

The build process automatically removes these classes using the `modify_dex.py` script to ensure the launcher works properly.

## Advanced Usage

### Manual DEX Modification

If you want to customize which classes are included/excluded, you can modify the `modify_dex.py` script:

```python
# Edit the CLASSES_TO_REMOVE list in modify_dex.py
CLASSES_TO_REMOVE = [
    'com/mojang/minecraftpe/MainActivity',
    'com/mojang/minecraftpe/FilePickerManagerHandler',
    # Add or remove classes as needed
]
```

Then run the script manually:
```bash
python modify_dex.py
```

### Verification

To verify your DEX file contents:

```bash
# Check DEX file size and classes (requires Android SDK)
$ANDROID_HOME/build-tools/*/dexdump -f build/libs/launcher.dex | grep "Class descriptor"
```

## Related Projects

- **[LeviLaunchroid](https://github.com/LiteLDev/LeviLaunchroid)** - The main Android launcher application
