#!/bin/bash

echo "Building LauncherDex..."

# Check if ANDROID_HOME is set
if [ -z "$ANDROID_HOME" ]; then
    echo "ERROR: ANDROID_HOME environment variable is not set"
    echo "Please set ANDROID_HOME to your Android SDK path"
    exit 1
fi

# Check if Java is available
if ! command -v java &> /dev/null; then
    echo "ERROR: Java is not installed or not in PATH"
    echo "Please install Java and add it to your PATH"
    exit 1
fi

# Create build directories
mkdir -p build/classes
mkdir -p build/libs

echo "Compiling Java sources..."
javac -cp "$ANDROID_HOME/platforms/android-31/android.jar" -d build/classes \
    src/main/java/com/mojang/minecraftpe/*.java \
    src/main/java/com/mojang/minecraftpe/store/*.java \
    src/main/java/com/mojang/minecraftpe/store/amazonappstore/*.java \
    src/main/java/com/mojang/minecraftpe/store/googleplay/*.java

if [ $? -ne 0 ]; then
    echo "ERROR: Java compilation failed"
    exit 1
fi

echo "Creating JAR file..."
jar cf build/libs/LauncherDex-1.0.jar -C build/classes .

if [ $? -ne 0 ]; then
    echo "ERROR: JAR creation failed"
    exit 1
fi

echo "Converting JAR to DEX..."
# Find the latest build-tools version
BUILD_TOOLS_VERSION=$(ls "$ANDROID_HOME/build-tools" | sort -V | tail -n 1)

if [ -z "$BUILD_TOOLS_VERSION" ]; then
    echo "ERROR: No build-tools found in Android SDK"
    exit 1
fi

echo "Using build-tools version: $BUILD_TOOLS_VERSION"
# Try d8 first (newer), then fall back to dx (older)
if [ -f "$ANDROID_HOME/build-tools/$BUILD_TOOLS_VERSION/d8.bat" ] || [ -f "$ANDROID_HOME/build-tools/$BUILD_TOOLS_VERSION/d8" ]; then
    echo "Using d8 tool..."
    if [ -f "$ANDROID_HOME/build-tools/$BUILD_TOOLS_VERSION/d8.bat" ]; then
        "$ANDROID_HOME/build-tools/$BUILD_TOOLS_VERSION/d8.bat" --output build/libs/ build/libs/LauncherDex-1.0.jar
    else
        "$ANDROID_HOME/build-tools/$BUILD_TOOLS_VERSION/d8" --output build/libs/ build/libs/LauncherDex-1.0.jar
    fi
    # d8 creates classes.dex, rename it
    if [ -f "build/libs/classes.dex" ]; then
        mv build/libs/classes.dex build/libs/launcher.dex
    fi
else
    echo "Using dx tool..."
    "$ANDROID_HOME/build-tools/$BUILD_TOOLS_VERSION/dx" --dex --output=build/libs/launcher.dex build/libs/LauncherDex-1.0.jar
fi

if [ $? -ne 0 ]; then
    echo "ERROR: DEX conversion failed"
    exit 1
fi

echo ""
echo "Build completed successfully!"
echo "JAR file: build/libs/LauncherDex-1.0.jar"
echo "DEX file: build/libs/launcher.dex"

# Run the DEX modification script to optimize the file
echo ""
echo "Optimizing DEX file..."
if command -v python3 &> /dev/null; then
    python3 modify_dex.py
elif command -v python &> /dev/null; then
    python modify_dex.py
else
    echo "Warning: Python not found. DEX file not optimized."
    echo "Run 'python modify_dex.py' manually to optimize the DEX file."
fi

echo ""
